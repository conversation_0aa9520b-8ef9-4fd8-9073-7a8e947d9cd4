package com.qmqb.imp.common.constant;

/**
 * 缓存的key 常量
 *
 * <AUTHOR>
 */
public interface CacheConstants {

    /**
     * 登录用户 redis key
     */
    String LOGIN_TOKEN_KEY = "Authorization:login:token:";

    /**
     * 在线用户 redis key
     */
    String ONLINE_TOKEN_KEY = "online_tokens:";

    /**
     * 验证码 redis key
     */
    String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 参数管理 cache key
     */
    String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    String SYS_DICT_KEY = "sys_dict:";

    /**
     * 防重提交 redis key
     */
    String REPEAT_SUBMIT_KEY = "repeat_submit:";

    /**
     * 限流 redis key
     */
    String RATE_LIMIT_KEY = "rate_limit:";

    /**
     * 登录账户密码错误次数 redis key
     */
    String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 值班时间redis key
     */
    String ONDUTY_TIME_KEY = "onduty_time:";

    /**
     * 禅道Token缓存 redis key
     */
    String ZENTAO_TOKEN_KEY = "zentao:token";

    /**
     * 禅道Token过期时间（秒）- 设置为1.5小时，实际Token有效期通常为2小时
     */
    long ZENTAO_TOKEN_TTL = 5400L;
}
