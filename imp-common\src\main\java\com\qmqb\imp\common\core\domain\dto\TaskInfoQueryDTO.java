package com.qmqb.imp.common.core.domain.dto;

import com.qmqb.imp.common.core.domain.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/12/15
 */
@Data
@Schema(description = "每月各组任务查询请求参数")
public class TaskInfoQueryDTO extends PageQuery {

    @Schema(description = "所属组别ID")
    private Long groupId;

    @Schema(description = "起始时间")
    private Date[] dateArr;

    @Schema(description = "任务状态")
    private String taskStatus;

    @Schema(description = "任务名称 模糊查询")
    private String name;

}
