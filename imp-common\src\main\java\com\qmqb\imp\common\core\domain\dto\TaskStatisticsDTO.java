package com.qmqb.imp.common.core.domain.dto;

import com.qmqb.imp.common.core.domain.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Data
@Schema(description = "任务统计请求参数")
public class TaskStatisticsDTO  extends PageQuery {

    @Schema(description = "起始时间")
    private Date[] dateArr;

}
