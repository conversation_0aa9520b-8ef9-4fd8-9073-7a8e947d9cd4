-- 创建项目成果表
CREATE TABLE tb_project_result
(
    id                      BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键ID' PRIMARY KEY,

    -- 基本信息字段
    business_type_id        BIGINT UNSIGNED   NOT NULL COMMENT '业务类型ID，关联业务类型管理表主键',
    project_task_name       VARCHAR(100)   NOT NULL COMMENT '项目/任务名称，限制30字符',
    priority_level          VARCHAR(2)     NOT NULL COMMENT '优先级：P1/P2/P3',
    status                  VARCHAR(2)     NOT NULL COMMENT '状态：1未开始、2进行中、3已完成、4已取消',

    -- 项目里程碑字段（拆分）
    milestone_requirements  DATETIME NULL COMMENT '完成评审时间',
    milestone_development   DATETIME NULL COMMENT '完成开发时间',
    milestone_test          DATETIME NULL COMMENT '完成测试验收时间',
    milestone_online        DATETIME NULL COMMENT '完成上线时间',

    -- 任务说明/进度字段（拆分）
    requirements_progress   DECIMAL(5, 2)  DEFAULT 0 NULL COMMENT '需求评审进度百分比',
    development_progress    DECIMAL(5, 2)  DEFAULT 0 NULL COMMENT '开发进度百分比',
    test_progress           DECIMAL(5, 2)  DEFAULT 0 NULL COMMENT '测试验收进度百分比',

    -- 干系人字段（拆分）
    dev_teams               VARCHAR(1000)  DEFAULT '' NULL COMMENT '开发组，多个用逗号分隔',
    test_teams              VARCHAR(1000)  DEFAULT '' NULL COMMENT '测试组，多个用逗号分隔',
    product_teams           VARCHAR(1000)  DEFAULT '' NULL COMMENT '产品组，多个用逗号分隔',

    -- 投入人力字段（拆分）
    dev_manpower            INT            DEFAULT 0 NULL COMMENT '开发投入人力（人）',
    test_manpower           INT            DEFAULT 0 NULL COMMENT '测试投入人力（人）',

    -- 工作量字段（拆分）
    dev_workload            DECIMAL(10, 2) DEFAULT 0 NULL COMMENT '开发工作量（人日）',
    test_workload           DECIMAL(10, 2) DEFAULT 0 NULL COMMENT '测试工作量（人日）',

    -- 需求背景
    requirement_background  TEXT NULL COMMENT '需求背景',

    -- 业务分类字段
    business_category_major VARCHAR(2)     DEFAULT '' NULL COMMENT '所属业务大类：1国内、2海外',
    business_category_minor VARCHAR(2)     DEFAULT '' NULL COMMENT '所属业务小类：1风控、2营销、3资金、4资产、5贷后、6自营业务、7综合、8其它',

    -- 负责人字段（支持多选）
    project_managers        VARCHAR(500)   DEFAULT ''                NOT NULL COMMENT '负责项目经理，多个用逗号分隔',

    -- 时间字段
    completion_time         DATETIME NULL COMMENT '完成时间，仅状态为已完成时可填写',

    -- 归档标志
    archive_flag            TINYINT(1) UNSIGNED DEFAULT 0                 NOT NULL COMMENT '归档标志：0未归档、1已归档',

    -- 系统字段
    created_by              VARCHAR(30)    DEFAULT ''                NOT NULL COMMENT '创建人',
    created_time            DATETIME       DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    updated_by              VARCHAR(30)    DEFAULT ''                NOT NULL COMMENT '更新人',
    updated_time            DATETIME       DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag                TINYINT(4) UNSIGNED DEFAULT 0                 NULL COMMENT '删除标志（0代表存在 2代表删除）'
) COMMENT '项目管理主表' CHARSET = utf8mb4;
