package com.qmqb.imp.system.service;

import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.bo.ProjectResultBo;
import com.qmqb.imp.system.domain.vo.ProjectResultVo;

import java.util.Collection;
import java.util.List;

/**
 * 项目成果表Service接口
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface IProjectResultService {

    /**
     * 查询项目成果表
     * @param id 项目成果表ID
     * @return 项目成果表
     */
    ProjectResultVo queryById(Long id);

    /**
     * 查询项目成果表列表
     * @param bo 查询条件
     * @param pageQuery 分页查询条件
     * @return 项目成果表列表
     */
    TableDataInfo<ProjectResultVo> queryPageList(ProjectResultBo bo, PageQuery pageQuery);

    /**
     * 查询项目成果表列表
     * @param bo 查询条件
     * @return 项目成果表列表
     */
    List<ProjectResultVo> queryList(ProjectResultBo bo);

    /**
     * 新增项目成果表
     * @param bo 新增条件
     * @return 是否成功
     */
    Boolean insertByBo(ProjectResultBo bo);

    /**
     * 修改项目成果表
     * @param bo 修改条件
     * @return 是否成功
     */
    Boolean updateByBo(ProjectResultBo bo);

    /**
     * 校验并批量删除项目成果表信息
     * @param ids 需要删除的项目成果表ID
     * @param isValid 是否校验
     * @return 是否成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
