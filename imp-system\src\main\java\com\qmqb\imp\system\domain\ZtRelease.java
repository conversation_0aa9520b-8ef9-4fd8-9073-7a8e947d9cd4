package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 禅道发布表 zt_release
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("zt_release")
public class ZtRelease implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 所属项目ID或名称
     */
    private String project;

    /**
     * 所属产品ID
     */
    private Integer product;

    /**
     * 所属分支名称
     */
    private String branch;

    /**
     * 影子分支ID
     */
    private Integer shadow;

    /**
     * 关联构建版本名称
     */
    private String build;

    /**
     * 发布名称
     */
    private String name;

    /**
     * 所属系统/模块ID
     */
    @TableField("`system`")
    private Integer system;

    /**
     * 发布版本号
     */
    private String releases;

    /**
     * 标记位（0-普通，1-标记）
     */
    private String marker;

    /**
     * 计划发布日期
     */
    private Date date;

    /**
     * 实际发布日期
     */
    private Date releasedDate;

    /**
     * 关联需求ID列表
     */
    private String stories;

    /**
     * 关联BUG ID列表
     */
    private String bugs;

    /**
     * 未解决BUG ID列表
     */
    private String leftBugs;

    /**
     * 发布说明
     */
    @TableField("`desc`")
    private String desc;

    /**
     * 邮件通知列表
     */
    private String mailto;

    /**
     * 通知对象列表
     */
    private String notify;

    /**
     * 发布状态（normal/closed等）
     */
    private String status;

    /**
     * 发布子状态
     */
    private String subStatus;

    /**
     * 创建人账号
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 删除标志（0-未删，1-已删）
     */
    private String deleted;

} 