package com.qmqb.imp.common.core.domain.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 任务查询任务请求参数
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TaskQueryDTO extends BasePageDTO {


    /**
     * 创建年份
     */
    private String createYear;

    /**
     * 创建月份
     */
    private String createMonth;


    /**
     * 完成年份
     */
    private String finishYear;

    /**
     * 完成月份
     */
    private String finishMonth;

    /**
     * 完成人
     */
    private String completedBy;

    /**
     * 默认状态 -1 : all   0 : wait  1 : doing 2 : done 3 : pause 4 : closed
     */
    private String status;

    /**
     * 任务启动人
     */
    private String taskStarter;

    /**
     * 组别ID
     */
    private Long groupId;

    /**
     * 用户列表
     */
    private List<String> userNameList;

    /**
     * 排序列
     */
    private String orderByColumn;
    /**
     * 排序的方向:asc 或者 desc
     */
    private String sort = "asc";
}
