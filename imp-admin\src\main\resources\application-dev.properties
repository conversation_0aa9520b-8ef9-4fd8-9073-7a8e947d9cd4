# \u5F00\u53D1\u73AF\u5883\u914D\u7F6E
# \u670D\u52A1\u5668\u7684HTTP\u7AEF\u53E3\uFF0C\u9ED8\u8BA4\u4E3A8080
server.port=9999

# \u76D1\u63A7\u4E2D\u5FC3\u914D\u7F6E
# \u589E\u52A0\u5BA2\u6237\u7AEF\u5F00\u5173
spring.boot.admin.client.enabled=false
spring.boot.admin.client.url=http://localhost:9090/admin
spring.boot.admin.client.instance.service-host-type=IP
spring.boot.admin.client.username=ruoyi
spring.boot.admin.client.password=123456

# xxl-job \u914D\u7F6E
# \u6267\u884C\u5668\u5F00\u5173
xxl.job.enabled=true
# \u8C03\u5EA6\u4E2D\u5FC3\u5730\u5740\uFF1A\u5982\u8C03\u5EA6\u4E2D\u5FC3\u96C6\u7FA4\u90E8\u7F72\u5B58\u5728\u591A\u4E2A\u5730\u5740\u5219\u7528\u9017\u53F7\u5206\u9694\u3002
xxl.job.admin-addresses=http://job-center.qmwallet.vip/xxl-job-admin
# \u6267\u884C\u5668\u901A\u8BAFTOKEN\uFF1A\u975E\u7A7A\u65F6\u542F\u7528
xxl.job.access-token=
# \u6267\u884C\u5668AppName\uFF1A\u6267\u884C\u5668\u5FC3\u8DF3\u6CE8\u518C\u5206\u7EC4\u4F9D\u636E\uFF1B\u4E3A\u7A7A\u5219\u5173\u95ED\u81EA\u52A8\u6CE8\u518C
xxl.job.executor.appname=imp-job-executor-dev
# \u6267\u884C\u5668\u7AEF\u53E3\u53F7 \u6267\u884C\u5668\u4ECE9101\u5F00\u59CB\u5F80\u540E\u5199
xxl.job.executor.port=9101
# \u6267\u884C\u5668\u6CE8\u518C\uFF1A\u9ED8\u8BA4IP:PORT
xxl.job.executor.address=
# \u6267\u884C\u5668IP\uFF1A\u9ED8\u8BA4\u81EA\u52A8\u83B7\u53D6IP
xxl.job.executor.ip=
# \u6267\u884C\u5668\u8FD0\u884C\u65E5\u5FD7\u6587\u4EF6\u5B58\u50A8\u78C1\u76D8\u8DEF\u5F84
xxl.job.executor.logpath=/www/wwwlogs/${spring.application.name}/xxl-job
# \u6267\u884C\u5668\u65E5\u5FD7\u6587\u4EF6\u4FDD\u5B58\u5929\u6570\uFF1A\u5927\u4E8E3\u751F\u6548
xxl.job.executor.logretentiondays=30

# \u6570\u636E\u6E90\u914D\u7F6E
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
# \u52A8\u6001\u6570\u636E\u6E90\u6587\u6863 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
# \u6027\u80FD\u5206\u6790\u63D2\u4EF6(\u6709\u6027\u80FD\u635F\u8017 \u4E0D\u5EFA\u8BAE\u751F\u4EA7\u73AF\u5883\u4F7F\u7528)
spring.datasource.dynamic.p6spy=true
# \u8BBE\u7F6E\u9ED8\u8BA4\u7684\u6570\u636E\u6E90\u6216\u8005\u6570\u636E\u6E90\u7EC4,\u9ED8\u8BA4\u503C\u5373\u4E3A master
spring.datasource.dynamic.primary=gitlab
# \u4E25\u683C\u6A21\u5F0F \u5339\u914D\u4E0D\u5230\u6570\u636E\u6E90\u5219\u62A5\u9519
spring.datasource.dynamic.strict=true
# gitlab\u7EDF\u8BA1\u6570\u636E\u6E90
spring.datasource.dynamic.datasource.gitlab.type=${spring.datasource.type}
spring.datasource.dynamic.datasource.gitlab.driverClassName=com.mysql.cj.jdbc.Driver
# jdbc \u6240\u6709\u53C2\u6570\u914D\u7F6E\u53C2\u8003 https://lionli.blog.csdn.net/article/details/122018562
# rewriteBatchedStatements=true \u6279\u5904\u7406\u4F18\u5316 \u5927\u5E45\u63D0\u5347\u6279\u91CF\u63D2\u5165\u66F4\u65B0\u5220\u9664\u6027\u80FD(\u5BF9\u6570\u636E\u5E93\u6709\u6027\u80FD\u635F\u8017 \u4F7F\u7528\u6279\u91CF\u64CD\u4F5C\u5E94\u8003\u8651\u6027\u80FD\u95EE\u9898)
spring.datasource.dynamic.datasource.gitlab.url=**********************************************************************************************************************************************************************************************************************
spring.datasource.dynamic.datasource.gitlab.username=u_jixiao
spring.datasource.dynamic.datasource.gitlab.password=VxtwAxUf53gMuKjuXqh5Vq6JS
# \u7985\u9053\u6570\u636E\u6E90
spring.datasource.dynamic.datasource.zentao.type=${spring.datasource.type}
spring.datasource.dynamic.datasource.zentao.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.zentao.url=**********************************************************************************************************************************************************************************************************************
spring.datasource.dynamic.datasource.zentao.username=u_readall
spring.datasource.dynamic.datasource.zentao.password=Qmqb#2020
# \u6162sql\u6570\u636E\u6E90
#spring.datasource.dynamic.datasource.slowsql.type=${spring.datasource.type}
#spring.datasource.dynamic.datasource.slowsql.driverClassName=com.mysql.cj.jdbc.Driver
#spring.datasource.dynamic.datasource.slowsql.url=*******************************************************************************************************************************************************************************************************
#spring.datasource.dynamic.datasource.slowsql.username=u_slow_sql
#spring.datasource.dynamic.datasource.slowsql.password=HkchxygthKatWJbS5mmUOurzZ
# HikariCP\u914D\u7F6E
# \u6700\u5927\u8FDE\u63A5\u6C60\u6570\u91CF
spring.datasource.dynamic.hikari.maxPoolSize=20
# \u6700\u5C0F\u7A7A\u95F2\u7EBF\u7A0B\u6570\u91CF
spring.datasource.dynamic.hikari.minIdle=10
# \u914D\u7F6E\u83B7\u53D6\u8FDE\u63A5\u7B49\u5F85\u8D85\u65F6\u7684\u65F6\u95F4
spring.datasource.dynamic.hikari.connectionTimeout=10000
# \u6821\u9A8C\u8D85\u65F6\u65F6\u95F4
spring.datasource.dynamic.hikari.validationTimeout=5000
# \u7A7A\u95F2\u8FDE\u63A5\u5B58\u6D3B\u6700\u5927\u65F6\u95F4\uFF0C\u9ED8\u8BA410\u5206\u949F
spring.datasource.dynamic.hikari.idleTimeout=60000
# \u6B64\u5C5E\u6027\u63A7\u5236\u6C60\u4E2D\u8FDE\u63A5\u7684\u6700\u957F\u751F\u547D\u5468\u671F\uFF0C\u503C0\u8868\u793A\u65E0\u9650\u751F\u547D\u5468\u671F\uFF0C\u9ED8\u8BA430\u5206\u949F
spring.datasource.dynamic.hikari.maxLifetime=900000
# \u8FDE\u63A5\u6D4B\u8BD5query\uFF08\u914D\u7F6E\u68C0\u6D4B\u8FDE\u63A5\u662F\u5426\u6709\u6548\uFF09
spring.datasource.dynamic.hikari.connectionTestQuery=SELECT 1

# redis \u5355\u673A\u914D\u7F6E(\u5355\u673A\u4E0E\u96C6\u7FA4\u53EA\u80FD\u5F00\u542F\u4E00\u4E2A\u53E6\u4E00\u4E2A\u9700\u8981\u6CE8\u91CA\u6389)
# \u5730\u5740
spring.redis.host=redis-local.qmwallet.vip
# \u7AEF\u53E3\uFF0C\u9ED8\u8BA4\u4E3A6379
spring.redis.port=4467
# \u6570\u636E\u5E93\u7D22\u5F15
spring.redis.database=210
# \u5BC6\u7801(\u5982\u6CA1\u6709\u5BC6\u7801\u8BF7\u6CE8\u91CA\u6389)
spring.redis.password=smu72fjs9bbshzp
# \u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4
spring.redis.timeout=10s
# \u662F\u5426\u5F00\u542Fssl
spring.redis.ssl=false

# redis key\u524D\u7F00
redisson.keyPrefix=
# \u7EBF\u7A0B\u6C60\u6570\u91CF
redisson.threads=4
# Netty\u7EBF\u7A0B\u6C60\u6570\u91CF
redisson.nettyThreads=8
# \u5355\u8282\u70B9\u914D\u7F6E
# \u5BA2\u6237\u7AEF\u540D\u79F0
redisson.singleServerConfig.clientName=${ruoyi.name}
# \u6700\u5C0F\u7A7A\u95F2\u8FDE\u63A5\u6570
redisson.singleServerConfig.connectionMinimumIdleSize=8
# \u8FDE\u63A5\u6C60\u5927\u5C0F
redisson.singleServerConfig.connectionPoolSize=32
# \u8FDE\u63A5\u7A7A\u95F2\u8D85\u65F6\uFF0C\u5355\u4F4D\uFF1A\u6BEB\u79D2
redisson.singleServerConfig.idleConnectionTimeout=10000
# \u547D\u4EE4\u7B49\u5F85\u8D85\u65F6\uFF0C\u5355\u4F4D\uFF1A\u6BEB\u79D2
redisson.singleServerConfig.timeout=3000
# \u53D1\u5E03\u548C\u8BA2\u9605\u8FDE\u63A5\u6C60\u5927\u5C0F
redisson.singleServerConfig.subscriptionConnectionPoolSize=50

# mail \u90AE\u4EF6\u53D1\u9001\u914D\u7F6E
mail.enabled=false
mail.host=smtp.163.com
mail.port=465
# \u662F\u5426\u9700\u8981\u7528\u6237\u540D\u5BC6\u7801\u9A8C\u8BC1
mail.auth=true
# \u53D1\u9001\u65B9\uFF0C\u9075\u5FAARFC-822\u6807\u51C6
mail.from=<EMAIL>
# \u7528\u6237\u540D\uFF08\u6CE8\u610F\uFF1A\u5982\u679C\u4F7F\u7528foxmail\u90AE\u7BB1\uFF0C\u6B64\u5904user\u4E3Aqq\u53F7\uFF09
mail.user=<EMAIL>
# \u5BC6\u7801\uFF08\u6CE8\u610F\uFF0C\u67D0\u4E9B\u90AE\u7BB1\u9700\u8981\u4E3ASMTP\u670D\u52A1\u5355\u72EC\u8BBE\u7F6E\u5BC6\u7801\uFF0C\u8BE6\u60C5\u67E5\u770B\u76F8\u5173\u5E2E\u52A9\uFF09
mail.pass=xxxxxxxxxx
# \u4F7F\u7528 STARTTLS\u5B89\u5168\u8FDE\u63A5\uFF0CSTARTTLS\u662F\u5BF9\u7EAF\u6587\u672C\u901A\u4FE1\u534F\u8BAE\u7684\u6269\u5C55\u3002
mail.starttlsEnable=true
# \u4F7F\u7528SSL\u5B89\u5168\u8FDE\u63A5
mail.sslEnable=true
# SMTP\u8D85\u65F6\u65F6\u957F\uFF0C\u5355\u4F4D\u6BEB\u79D2\uFF0C\u7F3A\u7701\u503C\u4E0D\u8D85\u65F6
mail.timeout=0
# Socket\u8FDE\u63A5\u8D85\u65F6\u503C\uFF0C\u5355\u4F4D\u6BEB\u79D2\uFF0C\u7F3A\u7701\u503C\u4E0D\u8D85\u65F6
mail.connectionTimeout=0

# sms \u77ED\u4FE1\u914D\u7F6E
sms.enabled=false
# \u963F\u91CC\u4E91 dysmsapi.aliyuncs.com
# \u817E\u8BAF\u4E91 sms.tencentcloudapi.com
sms.endpoint=dysmsapi.aliyuncs.com
sms.accessKeyId=xxxxxxxxx
sms.accessKeySecret=xxxxxxxxx
sms.signName=\u6D4B\u8BD5
# \u817E\u8BAF\u4E13\u7528
sms.sdkAppId=

# Sa-Token\u914D\u7F6E
# token\u540D\u79F0 (\u540C\u65F6\u4E5F\u662Fcookie\u540D\u79F0)
sa-token.token-name=Authorization
# token\u6709\u6548\u671F \u8BBE\u4E3A\u4E00\u5929 (\u5FC5\u5B9A\u8FC7\u671F) \u5355\u4F4D: \u79D2
sa-token.timeout=2592000
# token\u4E34\u65F6\u6709\u6548\u671F (\u6307\u5B9A\u65F6\u95F4\u65E0\u64CD\u4F5C\u5C31\u8FC7\u671F) \u5355\u4F4D: \u79D2
sa-token.activity-timeout=-1

# gitlab \u914D\u7F6E
gitlab-config.url=https://git.qmqb.top/
gitlab-config.token=********************

# p3c\u4EE3\u7801\u626B\u63CF\u914D\u7F6E
p3c.directory=D://p3c
p3c.branch=master

# \u6D88\u606F\u4E2D\u5FC3\u5730\u5740
message.url=http://**************:7711/message-center-api/v1/message/sendBase
message.appKey=impEOdzG2yK
message.appSecret=43fd5ffdcdb47952d3477582d265661a7bc38c01

# \u76D1\u63A7\u4E2D\u5FC3\u670D\u52A1\u5730\u5740
monitor-center.apiUrl=http://localhost:9008/monitor-api
dingtalk.robot-url=https://oapi.dingtalk.com/robot/send?access_token=4a3fd0ab2d004349b97bd1f722601c99226045603a3d95d8c4854c16791785e2
dingtalk.pm-robot-url=https://oapi.dingtalk.com/robot/send?access_token=4a3fd0ab2d004349b97bd1f722601c99226045603a3d95d8c4854c16791785e2
dingtalk.jszx-robot-url=https://oapi.dingtalk.com/robot/send?access_token=4a3fd0ab2d004349b97bd1f722601c99226045603a3d95d8c4854c16791785e2

# \u7845\u6D41AI\u914D\u7F6E
tool.siliconflow.api.key=sk-yibjgljjqvutefgpxjuugsoftuiirgwguttcazhzhsyorrnn

#\u5370\u5C3C\u7EE9\u6548\u7CFB\u7EDFurl
indonesia.jixiao.url=http://localhost:8181/index
indonesia.jixiao.apiUrl = http://localhost:8888/imp-admin

# \u7985\u9053API\u914D\u7F6E
zentao.api.url=https://pm.qmqb.top
zentao.api.username=jxxtpdzy
zentao.api.password=Qmqb#2025
zentao.api.execution-id=1244

# \u7EE9\u6548\u8F85\u5BFC\u9644\u4EF6\u4E0A\u4F20\u5730\u5740
file.storage.base-dir=D:/imp-admin/upload/
file.storage.performance-tutoring=uploadTutoring/

